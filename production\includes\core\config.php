<?php
/**
 * Configuration File
 *
 * This file contains all the configuration settings for the Pharaoh Finance Private and Fast Loans Management System.
 * Database connection details, site paths, and global constants are defined here.
 *
 * @package Pharaoh Finance Private and Fast Loans
 */

// Prevent direct access to this file
if (!defined('LENDSWIFT')) {
    die('Direct access to this file is not allowed.');
}

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', 'root');
define('DB_NAME', 'loan');
define('DB_CHARSET', 'utf8mb4');

// Site Configuration
define('SITE_NAME', 'Pharaoh Finance Private and Fast Loans');
define('SITE_TAGLINE', '');
define('SITE_DESCRIPTION', 'A user-friendly loan management system for borrowers and administrators.');

// Path Configuration
define('BASE_PATH', dirname(dirname(dirname(__FILE__)))); // Root directory path
define('INCLUDES_PATH', BASE_PATH . '/includes');
define('CORE_PATH', INCLUDES_PATH . '/core');
define('PAGES_PATH', BASE_PATH . '/pages');
define('ADMIN_PATH', BASE_PATH . '/admin');
define('BACKEND_PATH', BASE_PATH . '/backend');
define('ASSETS_PATH', BASE_PATH . '/assets');
define('UPLOADS_PATH', BASE_PATH . '/uploads');

// URL Configuration - Dynamic URL detection
// Determine the base URL dynamically
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    $path = preg_replace('#/[^/]*$#', '', $script);

    // If we're in the root directory
    if ($path === '/index.php' || $path === '') {
        $path = '';
    }

    return $protocol . $host . $path;
}

define('BASE_URL', getBaseUrl());
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');

// Email Configuration
define('MAIL_FROM', '<EMAIL>');
define('MAIL_FROM_NAME', SITE_NAME);
define('MAIL_REPLY_TO', '<EMAIL>');

// Security Configuration
define('HASH_COST', 12); // Cost parameter for password_hash
define('SESSION_NAME', 'lendswift_session');
define('CSRF_TOKEN_NAME', 'lendswift_csrf_token');
define('CSRF_TOKEN_EXPIRY', 3600); // 1 hour in seconds

// Session Configuration
ini_set('session.cookie_path', '/');
ini_set('session.cookie_domain', '');
ini_set('session.cookie_secure', false);
ini_set('session.cookie_httponly', true);
ini_set('session.use_only_cookies', true);

// Application Settings
define('DEFAULT_CURRENCY_ID', 1); // USD
define('DEFAULT_LANGUAGE', 'en');
define('DEFAULT_TIMEZONE', 'UTC');
define('ITEMS_PER_PAGE', 10);
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB in bytes
define('ALLOWED_UPLOAD_EXTENSIONS', ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx']);

// Development Settings
define('DEVELOPMENT_MODE', true); // Set to false in production

// Set default timezone
date_default_timezone_set(DEFAULT_TIMEZONE);

/**
 * Database Connection Function
 *
 * Creates and returns a mysqli database connection with appropriate error handling.
 *
 * @return mysqli Database connection object
 */
function getDbConnection() {
    static $mysqli = null;

    if ($mysqli === null) {
        // Create connection
        $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

        // Check connection
        if ($mysqli->connect_error) {
            error_log('Database Connection Error: ' . $mysqli->connect_error);
            die('Database connection failed. Please try again later or contact support.');
        }

        // Set charset
        $mysqli->set_charset(DB_CHARSET);
    }

    return $mysqli;
}
