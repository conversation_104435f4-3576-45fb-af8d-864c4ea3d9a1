<?php
/**
 * Admin Add User
 *
 * This file contains the functionality to add a new user.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Include email functions
require_once '../includes/email_functions.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect('login.php');
}

// Get admin information
$admin_id = get_current_admin_id();
$admin_name = $_SESSION['admin_name'] ?? 'Administrator';

// Get database connection
$db = getDbConnection();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token()) {
        set_flash_message('error', 'Invalid form submission. Please try again.');
        redirect('add-user.php');
    }

    // Get form data
    $name = ucwords(sanitize_input($_POST['name'] ?? ''));
    $email = sanitize_input($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $currency_id = (int)($_POST['currency_id'] ?? 1);
    $status = sanitize_input($_POST['status'] ?? 'active');

    // Validate form data
    $errors = [];

    if (empty($name)) {
        $errors[] = 'Name is required.';
    }

    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format.';
    } else {
        // Check if email already exists
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $errors[] = 'Email already exists.';
        }
    }

    if (empty($password)) {
        $errors[] = 'Password is required.';
    } elseif (strlen($password) < 8) {
        $errors[] = 'Password must be at least 8 characters.';
    } elseif ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match.';
    }

    // If no errors, add user
    if (empty($errors)) {
        try {
            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // Insert user
            $stmt = $db->prepare("
                INSERT INTO users (name, email, password, currency_id, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, NOW(), NOW())
            ");

            $stmt->bind_param("sssis", $name, $email, $hashed_password, $currency_id, $status);
            $stmt->execute();

            // Get the new user ID
            $user_id = $db->insert_id;

            // Record initial status in history
            $admin_id = $_SESSION['admin_id'] ?? 1;
            $admin_note = "Initial status set during user creation.";
            $history_stmt = $db->prepare("INSERT INTO user_status_history (user_id, status, changed_by, admin_note) VALUES (?, ?, ?, ?)");
            $history_stmt->bind_param("iiss", $user_id, $status, $admin_id, $admin_note);
            $history_stmt->execute();

            // Send welcome email to the new user
            $email_sent = send_welcome_email($user_id, $password);

            if ($email_sent) {
                set_flash_message('success', 'User added successfully and welcome email sent.');
            } else {
                set_flash_message('success', 'User added successfully but failed to send welcome email.');
            }

            redirect('users.php');
        } catch (Exception $e) {
            error_log('Add User Error: ' . $e->getMessage());
            set_flash_message('error', 'An error occurred. Please try again later.');
        }
    } else {
        // Set error messages
        foreach ($errors as $error) {
            set_flash_message('error', $error);
        }
    }
}

// Get currencies
$currencies = [];
$result = $db->query("SELECT id, name, code, symbol FROM currencies ORDER BY name ASC");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $currencies[] = $row;
    }
}

// Include admin header
include '../includes/admin_header.php';
?>

<div class="add-user">
    <div class="page-header">
        <h1>Add New User</h1>
        <div class="page-actions">
            <a href="users.php" class="button button-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2>User Information</h2>
        </div>
        <div class="card-content">
            <form method="POST" action="add-user.php">
                <?php echo csrf_token_field(); ?>

                <div class="form-group">
                    <label for="name">Name <span class="required">*</span></label>
                    <input type="text" id="name" name="name" class="form-control" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                </div>

                <div class="form-group">
                    <label for="email">Email <span class="required">*</span></label>
                    <input type="email" id="email" name="email" class="form-control" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                </div>

                <div class="form-group">
                    <label for="password">Password <span class="required">*</span></label>
                    <input type="password" id="password" name="password" class="form-control" required>
                    <p class="field-hint">Password must be at least 8 characters.</p>
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirm Password <span class="required">*</span></label>
                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="currency_id">Preferred Currency</label>
                    <div class="select-wrapper">
                        <select id="currency_id" name="currency_id" class="form-control styled-select">
                            <?php foreach ($currencies as $currency): ?>
                                <option value="<?php echo $currency['id']; ?>" <?php echo ($currency['id'] == ($_POST['currency_id'] ?? 1)) ? 'selected' : ''; ?>>
                                    <?php echo $currency['symbol']; ?> <?php echo htmlspecialchars($currency['code']); ?> - <?php echo htmlspecialchars($currency['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="select-arrow">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="status">Status</label>
                    <div class="select-wrapper">
                        <select id="status" name="status" class="form-control styled-select">
                            <option value="active" <?php echo ($_POST['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($_POST['status'] ?? 'active') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                        <div class="select-arrow">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="button button-primary">
                        <i class="fas fa-plus"></i> Add User
                    </button>
                    <a href="users.php" class="button button-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .add-user {
        margin-bottom: 2rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .card {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .card-header h2 {
        margin: 0;
        font-size: 1.25rem;
    }

    .card-content {
        padding: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .field-hint {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .required {
        color: #ef4444;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 500;
        border-radius: 0.5rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .button i {
        margin-right: 0.5rem;
    }

    .button-primary {
        background-color: #4f46e5;
        color: #fff;
    }

    .button-primary:hover {
        background-color: #4338ca;
    }

    .button-secondary {
        background-color: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
    }

    .button-secondary:hover {
        background-color: #e5e7eb;
    }

    /* Styled Select */
    .select-wrapper {
        position: relative;
        width: 100%;
    }

    .styled-select {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 100%;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        background-color: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .styled-select:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .select-arrow {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #6b7280;
    }
</style>

<?php
// Include admin footer
include '../includes/admin_footer.php';
?>
