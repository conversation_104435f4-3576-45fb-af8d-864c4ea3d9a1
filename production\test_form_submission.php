<?php
/**
 * Test Form Submission
 * 
 * This file tests form submission to ensure no double admin/ issues
 */

// Include the configuration
require_once 'includes/core/config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Submission Test - LendSwift Production</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-result { background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        h1 { color: #333; }
        code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        .form-test { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin: 20px 0; }
        .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .button:hover { background: #0056b3; }
        .form-control { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>LendSwift Production - Form Submission Test</h1>
    
    <div class="test-result info">
        <h3>📋 Configuration Information</h3>
        <p><strong>Detected BASE_URL:</strong> <code><?php echo BASE_URL; ?></code></p>
        <p><strong>Current Page:</strong> <code><?php echo $_SERVER['REQUEST_URI']; ?></code></p>
        <p><strong>Test Purpose:</strong> Verify form submissions don't create double admin/ paths</p>
    </div>

    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
        <div class="test-result success">
            <h3>✅ Form Submission Test Result</h3>
            <p><strong>Form submitted successfully!</strong></p>
            <p><strong>Action URL:</strong> <code><?php echo $_SERVER['REQUEST_URI']; ?></code></p>
            <p><strong>Submitted Data:</strong></p>
            <ul>
                <?php foreach ($_POST as $key => $value): ?>
                    <li><strong><?php echo htmlspecialchars($key); ?>:</strong> <?php echo htmlspecialchars($value); ?></li>
                <?php endforeach; ?>
            </ul>
            <p><strong>✅ No double admin/ path detected!</strong></p>
        </div>
    <?php endif; ?>

    <div class="form-test">
        <h3>🧪 Test Form (Relative Action)</h3>
        <p>This form uses a relative action path to avoid double admin/ issues:</p>
        <form method="POST" action="test_form_submission.php">
            <div>
                <label>Test Field:</label>
                <input type="text" name="test_field" class="form-control" value="Test Value" required>
            </div>
            <div>
                <label>Test Select:</label>
                <select name="test_select" class="form-control">
                    <option value="option1">Option 1</option>
                    <option value="option2">Option 2</option>
                </select>
            </div>
            <div style="margin-top: 15px;">
                <button type="submit" class="button">Submit Test Form</button>
            </div>
        </form>
        <p><small><strong>Form Action:</strong> <code>action="test_form_submission.php"</code> (relative path)</small></p>
    </div>

    <div class="form-test">
        <h3>❌ Bad Example (Would Create Double Admin)</h3>
        <p>This is what NOT to do (commented out to prevent issues):</p>
        <code>&lt;form action="&lt;?php echo BASE_URL; ?&gt;/admin/test_form_submission.php"&gt;</code>
        <p><small>This would create: <code><?php echo BASE_URL; ?>/admin/test_form_submission.php</code></small></p>
        <p><small>Which becomes: <code>http://localhost/pfloans.com/production/admin/admin/test_form_submission.php</code> ❌</small></p>
    </div>

    <div class="test-result">
        <h3>🔧 Admin Page Tests</h3>
        <p><a href="admin/add-user.php" class="button">Test Add User Page</a></p>
        <p><a href="admin/users.php" class="button">Test Users List</a></p>
        <p><a href="admin/index.php" class="button">Test Admin Dashboard</a></p>
        <p><small>These links use relative paths and should work correctly</small></p>
    </div>

    <div class="test-result info">
        <h3>✅ Best Practices</h3>
        <ul>
            <li><strong>Form Actions:</strong> Use <code>action="filename.php"</code> (relative)</li>
            <li><strong>Navigation Links:</strong> Use <code>href="filename.php"</code> (relative)</li>
            <li><strong>Redirects:</strong> Use <code>redirect('filename.php')</code> (relative)</li>
            <li><strong>AJAX URLs:</strong> Use relative paths in JavaScript</li>
            <li><strong>Avoid:</strong> <code>BASE_URL . '/admin/'</code> patterns in admin pages</li>
        </ul>
    </div>
    
    <p><a href="index.php">← Go to Main Application</a></p>
</body>
</html>
