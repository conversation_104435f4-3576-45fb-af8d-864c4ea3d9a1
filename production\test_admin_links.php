<?php
/**
 * Admin Links Test
 * 
 * This file tests all admin links and flows to identify broken links
 */

// Include the configuration
require_once 'includes/core/config.php';

// Admin pages to test
$admin_pages = [
    'index.php' => 'Admin Dashboard',
    'add-user.php' => 'Add User',
    'users.php' => 'Users List',
    'edit-user.php' => 'Edit User',
    'view-user.php' => 'View User',
    'applications.php' => 'Applications',
    'application-details.php' => 'Application Details',
    'transactions.php' => 'Transactions',
    'edit-transaction.php' => 'Edit Transaction',
    'reports.php' => 'Reports',
    'settings.php' => 'Settings',
    'currencies.php' => 'Currencies',
    'loan-products.php' => 'Loan Products',
    'payment-methods.php' => 'Payment Methods',
    'statuses.php' => 'Statuses',
    'email-templates.php' => 'Email Templates',
    'notifications.php' => 'Notifications',
    'support.php' => 'Support',
    'login.php' => 'Admin Login',
    'logout.php' => 'Admin Logout'
];

// AJAX endpoints to test
$ajax_endpoints = [
    'ajax/get-notification-count.php' => 'Get Notification Count',
    'ajax/mark_notification_read.php' => 'Mark Notification Read',
    'ajax/mark_all_notifications_read.php' => 'Mark All Notifications Read',
    'ajax/create-test-notification.php' => 'Create Test Notification'
];

// Function to test URL accessibility
function testUrl($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'status_code' => $httpCode,
        'error' => $error,
        'accessible' => ($httpCode >= 200 && $httpCode < 400)
    ];
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Links Test - LendSwift Production</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-result { background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        h1 { color: #333; }
        code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <h1>LendSwift Production - Admin Links Test</h1>
    
    <div class="test-result info">
        <h3>📋 Configuration Information</h3>
        <p><strong>Detected BASE_URL:</strong> <code><?php echo BASE_URL; ?></code></p>
        <p><strong>Admin Path:</strong> <code><?php echo BASE_URL; ?>/admin/</code></p>
        <p><strong>Note:</strong> Admin pages should use relative paths to avoid double admin/ issue</p>
        <p><strong>Test Time:</strong> <code><?php echo date('Y-m-d H:i:s'); ?></code></p>
    </div>

    <h2>🔗 Admin Pages Test</h2>
    <table>
        <thead>
            <tr>
                <th>Page</th>
                <th>URL</th>
                <th>Status</th>
                <th>HTTP Code</th>
                <th>Notes</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($admin_pages as $page => $title): ?>
                <?php 
                $url = BASE_URL . '/admin/' . $page;
                $test = testUrl($url);
                $statusClass = $test['accessible'] ? 'status-ok' : 'status-error';
                $statusText = $test['accessible'] ? '✅ OK' : '❌ ERROR';
                ?>
                <tr>
                    <td><?php echo htmlspecialchars($title); ?></td>
                    <td><code><?php echo htmlspecialchars($url); ?></code></td>
                    <td class="<?php echo $statusClass; ?>"><?php echo $statusText; ?></td>
                    <td><?php echo $test['status_code']; ?></td>
                    <td><?php echo $test['error'] ? htmlspecialchars($test['error']) : '-'; ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <h2>🔌 AJAX Endpoints Test</h2>
    <table>
        <thead>
            <tr>
                <th>Endpoint</th>
                <th>URL</th>
                <th>Status</th>
                <th>HTTP Code</th>
                <th>Notes</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($ajax_endpoints as $endpoint => $title): ?>
                <?php 
                $url = BASE_URL . '/' . $endpoint;
                $test = testUrl($url);
                $statusClass = $test['accessible'] ? 'status-ok' : 'status-error';
                $statusText = $test['accessible'] ? '✅ OK' : '❌ ERROR';
                ?>
                <tr>
                    <td><?php echo htmlspecialchars($title); ?></td>
                    <td><code><?php echo htmlspecialchars($url); ?></code></td>
                    <td class="<?php echo $statusClass; ?>"><?php echo $statusText; ?></td>
                    <td><?php echo $test['status_code']; ?></td>
                    <td><?php echo $test['error'] ? htmlspecialchars($test['error']) : '-'; ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <div class="test-result">
        <h3>🚀 Next Steps</h3>
        <ul>
            <li>✅ Green status means the page/endpoint is accessible</li>
            <li>❌ Red status indicates broken links that need fixing</li>
            <li>Check HTTP codes: 200-299 = OK, 300-399 = Redirect, 400+ = Error</li>
            <li>Test actual functionality by clicking the links manually</li>
        </ul>
    </div>
    
    <div class="test-result info">
        <h3>🔧 Manual Testing Links</h3>
        <p><strong>Admin Dashboard:</strong> <a href="<?php echo BASE_URL; ?>/admin/" target="_blank">Open Admin</a></p>
        <p><strong>Add User Page:</strong> <a href="<?php echo BASE_URL; ?>/admin/add-user.php" target="_blank">Test Add User</a></p>
        <p><strong>Users List:</strong> <a href="<?php echo BASE_URL; ?>/admin/users.php" target="_blank">Test Users List</a></p>
    </div>
    
    <p><a href="index.php">← Go to Main Application</a></p>
</body>
</html>
